#!/bin/bash

# Vue 项目配置初始化脚本
# 使用方法: ./init-vue-project.sh

echo "🚀 正在初始化 Vue 项目配置..."

# 创建 .vscode 目录
mkdir -p .vscode

# 创建 jsconfig.json
echo "📝 创建 jsconfig.json..."
cat > jsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": [
    "src/**/*.vue",
    "src/**/*.js",
    "src/**/*.jsx",
    "src/**/*.ts",
    "src/**/*.tsx"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
EOF

# 创建 .vscode/settings.json
echo "⚙️  创建 .vscode/settings.json..."
cat > .vscode/settings.json << 'EOF'
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "javascript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "javascript.updateImportsOnFileMove.enabled": "always",
  "vue.codeActions.enabled": true,
  "vue.complete.casing.tags": "kebab",
  "vue.complete.casing.props": "camel"
}
EOF

# 创建 .vscode/extensions.json
echo "🔌 创建 .vscode/extensions.json..."
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode"
  ]
}
EOF

echo "✅ Vue 项目配置初始化完成！"
echo "📋 已创建的文件："
echo "   - jsconfig.json"
echo "   - .vscode/settings.json"
echo "   - .vscode/extensions.json"
echo ""
echo "🔄 请重启 VS Code 以使配置生效"
